// 测试修复后的代码
// 模拟小程序环境

// 模拟数据映射函数
function mapDefaultData(data) {
  // 确保数据是数组格式
  if (!Array.isArray(data)) {
    console.warn('数据不是数组格式，尝试转换:', data)
    if (data && typeof data === 'object') {
      // 如果是对象，尝试获取可能的数组属性
      const possibleArrays = ['list', 'data', 'items', 'records', 'rows']
      for (const key of possibleArrays) {
        if (Array.isArray(data[key])) {
          data = data[key]
          break
        }
      }
    }
    // 如果仍然不是数组，返回空数组
    if (!Array.isArray(data)) {
      return []
    }
  }
  
  return data.map((item) => ({
    label: item.label || item.dictLabel || item.name || item.title || String(item),
    value: item.value || item.dictValue || item.id || item.key || String(item),
  }))
}

// 模拟小程序环境的解析函数
function executeParseFn(parseStr, resData) {
  try {
    const cleanedStr = parseStr
      .replace('[[FORM-CREATE-PREFIX-', '')
      .replace('-FORM-CREATE-SUFFIX]]', '')
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    const functionBody = cleanedStr.substring(
      cleanedStr.indexOf('{') + 1,
      cleanedStr.lastIndexOf('}'),
    )
    
    // 在小程序环境中，new Function() 被禁用，需要使用替代方案
    console.warn('小程序环境不支持动态函数执行，使用默认数据映射')
    return mapDefaultData(resData)
  } catch (error) {
    console.error('解析函数执行失败，使用默认数据映射:', error)
    return mapDefaultData(resData)
  }
}

// 测试用例
console.log('=== 测试开始 ===')

// 测试1: 正常数组数据
const testData1 = [
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' }
]
console.log('测试1 - 正常数组数据:')
console.log('输入:', testData1)
console.log('输出:', mapDefaultData(testData1))

// 测试2: 对象包含数组数据
const testData2 = {
  data: [
    { dictLabel: '字典1', dictValue: 'dict1' },
    { dictLabel: '字典2', dictValue: 'dict2' }
  ]
}
console.log('\n测试2 - 对象包含数组数据:')
console.log('输入:', testData2)
console.log('输出:', mapDefaultData(testData2))

// 测试3: 使用解析函数（模拟小程序环境）
const parseStr = '[[FORM-CREATE-PREFIX-function(res) { return res.map(item => ({label: item.name, value: item.id})) }-FORM-CREATE-SUFFIX]]'
const testData3 = [
  { name: '名称1', id: 'id1' },
  { name: '名称2', id: 'id2' }
]
console.log('\n测试3 - 解析函数（小程序环境）:')
console.log('输入:', testData3)
console.log('解析字符串:', parseStr)
console.log('输出:', executeParseFn(parseStr, testData3))

// 测试4: 错误数据处理
const testData4 = null
console.log('\n测试4 - 错误数据处理:')
console.log('输入:', testData4)
console.log('输出:', mapDefaultData(testData4))

console.log('\n=== 测试完成 ===')
