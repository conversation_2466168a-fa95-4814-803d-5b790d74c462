<script lang="ts" setup name="TemplatePublishFile">
import { queryFormDataByOrderId } from '@/api/ess/flow/runApplication'
import { getTmpSignOrderAPI } from '@/api/ess/template/index'
import ApproveUsersList from './ApproveUsersList.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})
const formData = ref({})
const signOrder = ref(0) // 0-有序 1-无序
const loading = ref(true)

async function getData() {
  try {
    const res = await queryFormDataByOrderId(props.form.orderId)
    const orderRes = await getTmpSignOrderAPI(res.object.templateId)
    formData.value = res.object
    signOrder.value = orderRes.object
    console.log(signOrder.value)
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <view class="flow-custom">
    <fg-skeleton v-if="loading" col-height="50rpx" :row-count="10" />
    <wd-cell-group v-else>
      <view class="base-info overflow-hidden rounded-18rpx">
        <wd-cell vertical title="标题" :value="formData.flowTitle" />
        <wd-cell vertical title="申请原由" :value="formData.applyReason" />
        <wd-cell vertical title="文件" :value="formData.templateName">
          <view class="file-box">
            <image
              src="@/static/images/icon/file-icon.png"
              mode="widthFix"
            />
            {{ formData.templateName }}
          </view>
        </wd-cell>
        <wd-cell vertical title="盖章后电子文件名称" :value="formData.flowName" />
        <view class="inline-form-item">
          <text class="color-#6B6B6B">
            签署截止时间：
          </text>
          <text v-if="+formData.flowFlag !== 1">
            审批通过后{{ formData.deadline }}天
          </text>
          <text v-else>
            {{ formData.deadlineTime }}
          </text>
        </view>
      </view>

      <wd-gap bg-color="#F9F9F9" height="20rpx" />
      <view class="overflow-hidden rounded-18rpx bg-#fff">
        <wd-cell title="签署方">
          <text class="color-#6B6B6B">
            签署顺序：
          </text>
          {{ signOrder === 0 ? '有序' : '无序' }}
        </wd-cell>
        <view class="approve-users">
          <ApproveUsersList :sign-order="signOrder" :list="formData.approveDtoList" />
        </view>
      </view>
    </wd-cell-group>
  </view>
</template>

<style lang="scss" scoped>
.flow-custom {
  margin-bottom: 20rpx;
  :deep(.wd-cell-group) {
    background: transparent;
    .wd-cell-group__body {
      background: transparent;
    }
  }
  .base-info {
    background: #fff;
    :deep() {
      .wd-cell__wrapper.is-vertical {
        padding-bottom: 0;
        .wd-cell__right {
          background: #f9f9f9;
          border-radius: 10rpx;
          margin-top: 10rpx;
          padding: 16rpx 30rpx;
        }
      }
    }
    .file-box {
      @apply flex-y-center;
      image {
        @apply w-28rpx flex-shrink-0 mr-14rpx;
      }
    }
  }
  .inline-form-item {
    font-size: 26rpx;
    padding: 20rpx 30rpx;
  }
}
</style>
