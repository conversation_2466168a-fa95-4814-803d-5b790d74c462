<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    loading?: boolean
    colHeight: string
    rowCount: number
  }>(),
  {
    loading: false,
    colHeight: '200rpx',
    rowCount: 6,
  },
)

const rowCol = computed(() => {
  const rowCol = []
  for (let i = 0; i < props.rowCount; i++) {
    rowCol.push({ width: '90%', height: props.colHeight, marginLeft: '5%', borderRadius: '10rpx' })
  }
  return rowCol
})
</script>

<template>
  <view class="">
    <wd-skeleton
      theme="paragraph"
      :row-col="rowCol"
    />
  </view>
</template>

<style lang="scss" scoped>
//
</style>
