// 测试修复后的代码
// 模拟小程序环境

// 模拟数据映射函数
function mapDefaultData(data) {
  // 确保数据是数组格式
  if (!Array.isArray(data)) {
    console.warn('数据不是数组格式，尝试转换:', data)
    if (data && typeof data === 'object') {
      // 如果是对象，尝试获取可能的数组属性
      const possibleArrays = ['list', 'data', 'items', 'records', 'rows']
      for (const key of possibleArrays) {
        if (Array.isArray(data[key])) {
          data = data[key]
          break
        }
      }
    }
    // 如果仍然不是数组，返回空数组
    if (!Array.isArray(data)) {
      return []
    }
  }

  return data.map((item) => ({
    label: item.label || item.dictLabel || item.name || item.title || String(item),
    value: item.value || item.dictValue || item.id || item.key || String(item),
  }))
}

// 智能解析函数 - 支持小程序环境
function parseDataWithPattern(parseStr, resData) {
  // 尝试从解析字符串中提取映射模式
  const labelMatch = parseStr.match(/label:\s*(\w+)\.(\w+)/);
  const valueMatch = parseStr.match(/value:\s*(\w+)\.(\w+)/);

  if (labelMatch && valueMatch) {
    const labelField = labelMatch[2]; // 例如: deptName
    const valueField = valueMatch[2]; // 例如: deptId

    console.log(`检测到映射模式: label=${labelField}, value=${valueField}`);

    if (Array.isArray(resData)) {
      return resData.map((item) => ({
        label: item[labelField] || item.label || item.name || String(item),
        value: item[valueField] || item.value || item.id || String(item),
      }));
    }
  }

  // 如果无法解析模式，使用默认映射
  return mapDefaultData(resData);
}

// 模拟小程序环境的解析函数
function executeParseFn(parseStr, resData) {
  try {
    const cleanedStr = parseStr
      .replace('[[FORM-CREATE-PREFIX-', '')
      .replace('-FORM-CREATE-SUFFIX]]', '')
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    // 在小程序环境中，使用智能解析替代动态函数执行
    console.warn('小程序环境不支持动态函数执行，使用智能解析')
    return parseDataWithPattern(cleanedStr, resData)
  } catch (error) {
    console.error('解析函数执行失败，使用默认数据映射:', error)
    return mapDefaultData(resData)
  }
}

// 测试用例
console.log('=== 测试开始 ===')

// 测试1: 正常数组数据
const testData1 = [
  { label: '选项1', value: '1' },
  { label: '选项2', value: '2' }
]
console.log('测试1 - 正常数组数据:')
console.log('输入:', testData1)
console.log('输出:', mapDefaultData(testData1))

// 测试2: 对象包含数组数据
const testData2 = {
  data: [
    { dictLabel: '字典1', dictValue: 'dict1' },
    { dictLabel: '字典2', dictValue: 'dict2' }
  ]
}
console.log('\n测试2 - 对象包含数组数据:')
console.log('输入:', testData2)
console.log('输出:', mapDefaultData(testData2))

// 测试3: 使用解析函数（模拟小程序环境）
const parseStr = '[[FORM-CREATE-PREFIX-function(res) { return res.map(item => ({label: item.name, value: item.id})) }-FORM-CREATE-SUFFIX]]'
const testData3 = [
  { name: '名称1', id: 'id1' },
  { name: '名称2', id: 'id2' }
]
console.log('\n测试3 - 解析函数（小程序环境）:')
console.log('输入:', testData3)
console.log('解析字符串:', parseStr)
console.log('输出:', executeParseFn(parseStr, testData3))

// 测试4: 使用你的实际数据（部门数据）
const realParseStr = '[[FORM-CREATE-PREFIX-function parse(res, rule, api){const options = res.map(dept => ({ label: dept.deptName, value: dept.deptId })); return options;}-FORM-CREATE-SUFFIX]]'
const realTestData = [
  { deptName: '技术部', deptId: 'tech001' },
  { deptName: '人事部', deptId: 'hr002' },
  { deptName: '财务部', deptId: 'finance003' }
]
console.log('\n测试4 - 实际部门数据解析:')
console.log('输入:', realTestData)
console.log('解析字符串:', realParseStr)
console.log('输出:', executeParseFn(realParseStr, realTestData))

// 测试5: 错误数据处理
const testData5 = null
console.log('\n测试5 - 错误数据处理:')
console.log('输入:', testData5)
console.log('输出:', mapDefaultData(testData5))

console.log('\n=== 测试完成 ===')
