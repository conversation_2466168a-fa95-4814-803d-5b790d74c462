import { essPrefix, json<PERSON>lowOrder, jsonFlowPrefix } from '@/config/constant'
import { http } from '@/utils/http'

interface PageParams {
  page: number
  limit: number
  [key: string]: any
}
// 获取流程分组
export function getTodoTypeGroupAPI() {
  return http.get(`${jsonFlowPrefix}/runFlowExt/queryMinProTodoType`, {})
}

// 获取待办任务
export function fetchTodoPageAPI(params: any) {
  return http.get(`${jsonFlowPrefix}/run-job/todo/page`, params)
}

// 我的待办-待我签署列表 带分页
export function mySigningPageAPI(data: PageParams) {
  return http.post(`${essPrefix}/todo/mySigningPage`, data)
}

// 进行中---流程 mine:0我发起的 1他人发起的
export function onGoingFlowPageAPI(params: any) {
  return http.get(`${jsonFlowPrefix}/runFlowExt/onGoingFlowPage`, params)
}

// 进行中-签署-----mine:0我发起的 1他人发起的
export function getDoingSignListAPI(data: PageParams) {
  if (data.mine === 0) {
    return http.post(`${essPrefix}/todo/myInitiationPage`, data)
  }
  else {
    return http.post(`${essPrefix}/todo/otherInitiationPage`, data)
  }
}

// 进行中-签署-他人发起列表 带分页
export function otherInitiationPage(data: PageParams) {
  return http.post(`${essPrefix}/todo/otherInitiationPage`, data)
}

// 即将超时-签署-我发起的列表 带分页
export function myApproachingPage(data: PageParams) {
  return http.post(`${essPrefix}/todo/myApproachingPage`, data)
}

// 即将超时-签署-他人发起的列表 带分页
export function otherApproachingPage(data: PageParams) {
  return http.post(`${essPrefix}/todo/otherApproachingPage`, data)
}

// 已完结-签署-获取已完结列表
export function getEndSignListAPI(data: PageParams) {
  if (data.mine === 0) {
    return http.post(`${essPrefix}/todo/myCompletePage`, data)
  }
  else {
    return http.post(`${essPrefix}/todo/otherCompletePage`, data)
  }
}

// 我的待办 - 已完结
export function getFlowFinishPageAPI(params: any) {
  return http.get(`${jsonFlowPrefix}/runFlowExt/finishPage`, params)
}

// 获取进行中-流程的数量
export function onGoingFlowCountAPI(data) {
  return http.get(`${jsonFlowPrefix}/runFlowExt/onGoingFlowCount`, data)
}

// 获取进行中-签署的数量
export function queryFileDoingCountAPI(data) {
  return http.post(`${essPrefix}/todo/queryFileDoingCount`, data)
}

// 获取已完结-流程的数量
export function getFlowFinishCountAPI(data) {
  return http.get(`${jsonFlowPrefix}/runFlowExt/finishCount`, data)
}

// 获取已完结-签署的数量
export function getFileFinishCountAPI(data) {
  return http.post(`${essPrefix}/todo/queryFileCompleteCount`, data)
}

// 流程详情
export function getFlowDetailInfoAPI(params) {
  return http.get(`${jsonFlowPrefix}/run-job/todo/detail`, params)
}
// 流程表单详情
export function getFlowFormDataAPI(id) {
  return http.get(`${jsonFlowOrder}/run-application/${id}`)
}
// 流程审批图
export function getFlowCommentAPI(params) {
  return http.get(`${jsonFlowPrefix}/comment/comment`, params)
}
// 获取流程表单配置项
export function getFlowFormOptionsAPI(params) {
  return http.get(`${jsonFlowPrefix}/form-option/option`, params)
}
// 获取签署的半屏拉起链接
export function getSignAppLinkAPI(essFlowId) {
  return http.get(`${essPrefix}/flow/signApp/${essFlowId}`)
}
// 获取签署方信息
export function exportOriginDataAPI(flowBatchPublishId: string) {
  return http({
    url: `${essPrefix}/flowInfoBatch/exportOriginData?flowBatchPublishId=${flowBatchPublishId}`,
    method: 'POST',
    responseType: 'arraybuffer', // 接收二进制数据
    header: {
      'Content-Type': 'application/json',
    },
  })
}
