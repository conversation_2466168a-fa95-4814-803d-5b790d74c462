<route lang="json5" type="page">
{
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "用户指南",
  },
}
</route>

<script lang="ts" setup>
import { getManualCfgListPage } from '@/api/ess/doc/manualCfgInfo'
import { imageApi } from '@/config/constant'

const paging = ref(null)
const loading = ref(true)
const dataList = ref([])
async function getList(pageNo, pageSize) {
  try {
    const res = await getManualCfgListPage({
      limit: pageSize,
      page: pageNo,
      belongType: 1,
    })
    paging.value.complete(res.object.records)
  }
  finally {
    loading.value = false
  }
}

// 我这里用的是 网络链接
function getPDF(e) {
  uni.downloadFile({
    url: imageApi + e,
    success(res) {
      const filePath = res.tempFilePath
      uni.openDocument({
        filePath,
        fileType: 'pdf',
      })
    },
    fail(res) {
      uni.showToast({
        title: '打开失败',
        icon: 'none',
      })
    },
  })
}
</script>

<template>
  <view class="app-container">
    <z-paging
      ref="paging"
      v-model="dataList"
      hide-no-more-inside
      auto-show-system-loading
      @query="getList"
    >
      <template #top>
        <fg-navbar>用户指南</fg-navbar>
      </template>
      <view class="manual-list">
        <view v-for="(item, index) in dataList" :key="index" class="manual-item" @click="getPDF(item.fileUrlSign)">
          <view class="title">
            {{ item.docName }}
          </view>
          <view class="ml-22rpx mt-10rpx color-#6B6B6B">
            {{ item.updateTime }}
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.manual-list {
  padding: 10rpx 30rpx;
  .manual-item {
    @apply bg-#fff p-y-20rpx p-x-30rpx rounded-18rpx;
    .title {
      @apply dot font-600;
    }
  }
}
</style>
