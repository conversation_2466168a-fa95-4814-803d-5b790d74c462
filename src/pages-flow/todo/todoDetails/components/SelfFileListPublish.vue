<script lang="ts" setup name="TemplatePublishFile">
import { queryFormDataByOrderId } from '@/api/ess/flow/runApplication'
import { useBusinessLines } from '@/components/filter-popup/useBusinessLines'
import { imageApi } from '@/config/constant'
import { getFileName } from '@/utils'
import ApproveUsersList from './ApproveUsersList.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})
const formData = ref({})
const loading = ref(true)
const { businessLines } = useBusinessLines()

async function getData() {
  try {
    const res = await queryFormDataByOrderId(props.form.orderId)
    formData.value = res.object
    formData.value.fileName = getFileName(formData.value.filePaths[0])
  }
  finally {
    loading.value = false
  }
}
// 从文件名获取文件扩展名
function getFileExtension(fileName: string): string {
  const lastDotIndex = fileName.lastIndexOf('.')
  return fileName.substring(lastDotIndex + 1).toLowerCase()
}

// 获取文件类型
function getFileType(fileName: string): 'image' | 'document' | 'html' | 'unsupported' {
  const extension = getFileExtension(fileName)

  if (['jpg', 'png'].includes(extension))
    return 'image'
  if (['pdf', 'doc', 'docx', 'xls', 'xlsx'].includes(extension))
    return 'document'
  if (['html'].includes(extension))
    return 'html'

  return 'unsupported'
}

// 预览图片
function previewImage(fileUrl: string) {
  uni.previewImage({
    urls: [fileUrl],
    current: fileUrl,
  })
}

// 处理 HTML 文件
function handleHtmlFile(fileUrl: string) {
  uni.showModal({
    title: '提示',
    content: 'HTML 文件无法在小程序中预览，请在浏览器中打开',
    showCancel: true,
    cancelText: '取消',
    confirmText: '复制链接',
    success(res) {
      if (res.confirm) {
        uni.setClipboardData({
          data: fileUrl,
          success() {
            uni.showToast({
              title: '链接已复制',
              icon: 'success',
            })
          },
        })
      }
    },
  })
}

// 预览文档
function previewDocument(fileUrl: string, fileName: string) {
  uni.downloadFile({
    url: fileUrl,
    success(res) {
      uni.openDocument({
        filePath: res.tempFilePath,
        fileType: getFileExtension(fileName),
      })
    },
    fail() {
      uni.showToast({
        title: '打开失败',
        icon: 'none',
      })
    },
  })
}

// 文件预览主函数
function handlePreviewFile() {
  const fileName = formData.value.fileName
  const fileUrl = imageApi + formData.value.filePaths[0]
  const fileType = getFileType(fileName)

  switch (fileType) {
    case 'image':
      previewImage(fileUrl)
      break
    case 'html':
      handleHtmlFile(fileUrl)
      break
    case 'document':
      previewDocument(fileUrl, fileName)
      break
    default:
      uni.showToast({
        title: '不支持的文件类型',
        icon: 'none',
      })
  }
}

const businessLineName = computed(() => {
  return businessLines.value.find(item => item.id === formData.value.businessLineId)
    ?.businessLineName
})

onMounted(() => {
  getData()
})
</script>

<template>
  <view class="flow-custom">
    <fg-skeleton v-if="loading" col-height="50rpx" :row-count="10" />
    <wd-cell-group v-else>
      <view class="base-info overflow-hidden rounded-18rpx">
        <wd-cell vertical title="标题" :value="formData.flowTitle" />
        <wd-cell vertical title="发起原由" :value="formData.applyReason" />
        <wd-cell vertical title="申请附件" :value="formData.fileName">
          <view class="file-box" @click="handlePreviewFile">
            <view class="mr-10rpx flex-y-center">
              <image src="@/static/images/icon/file-icon.png" mode="widthFix" />
              <view class="max-w-[60vw] text-overflow">
                {{ formData.fileName }}
              </view>
            </view>
            <wd-icon class="mr-[-5rpx] flex-shrink-0" color="#707070" name="arrow-right" size="26rpx" />
          </view>
        </wd-cell>
        <wd-cell vertical title="盖章后电子文件名称" :value="formData.flowName" />
        <view class="inline-form-item">
          <text class="color-#6B6B6B">
            文件所属业务线：
          </text>
          <text>
            {{ businessLineName }}
          </text>
        </view>
        <view class="inline-form-item">
          <text class="color-#6B6B6B">
            签署截止时间：
          </text>
          <text v-if="+formData.flowFlag !== 1">
            审批通过后{{ formData.deadline }}天
          </text>
          <text v-else>
            {{ formData.deadlineTime }}
          </text>
        </view>
      </view>

      <wd-gap bg-color="#F9F9F9" height="20rpx" />
      <view class="overflow-hidden rounded-18rpx bg-#fff">
        <wd-cell title="签署方">
          <text class="color-#6B6B6B">
            签署顺序：
          </text>
          {{ formData.unordered ? "无序" : "有序" }}
        </wd-cell>
        <view class="approve-users">
          <ApproveUsersList
            :sign-order="formData.unordered ? 1 : 0"
            :list="formData.approveDtoList"
          />
        </view>
      </view>
    </wd-cell-group>
  </view>
</template>

<style lang="scss" scoped>
.flow-custom {
  margin-bottom: 20rpx;
  :deep(.wd-cell-group) {
    background: transparent;
    .wd-cell-group__body {
      background: transparent;
    }
  }
  .base-info {
    background: #fff;
    :deep() {
      .wd-cell__wrapper.is-vertical {
        padding-bottom: 0;
        .wd-cell__right {
          background: #f9f9f9;
          border-radius: 10rpx;
          margin-top: 10rpx;
          padding: 16rpx 30rpx;
        }
      }
      .wd-cell__wrapper:not(.is-vertical) {
      }
    }
    .file-box {
      @apply flex-x-between;
      image {
        @apply w-28rpx flex-shrink-0 mr-14rpx;
      }
    }
  }
  .inline-form-item {
    font-size: 26rpx;
    padding: 20rpx 30rpx;
  }
}
</style>
