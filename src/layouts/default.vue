<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const themeVars: ConfigProviderThemeVars = {
  colorTheme: '#24A87E',
  cellPadding: '30rpx',
  cellValueColor: '#0C1433',
  checkboxLabelFs: '28rpx',
  radioButtonFs: '28rpx',
  buttonInfoPlainBorderColor: '#CBCBCB',
  buttonInfoColor: '#38383A',
  buttonMediumHeight: '80rpx',
  buttonSmallHeight: '64rpx',
}
</script>

<template>
  <wd-config-provider :theme-vars="themeVars" class="page-container">
    <view class="router-main">
      <slot />
    </view>
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>
