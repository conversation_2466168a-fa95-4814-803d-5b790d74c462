<route lang="json5" type="page">
{
  layout: "default",
  needLogin: true,
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "详情",
  },
}
</route>

<script lang="ts" setup name="TodoDetails">
import { useMessage } from 'wot-design-uni'
import {
  backPreAPI,
  completeJobAPI,
  rejectJobAPI,
  saveFlowRunFormAPI,
} from '@/api/ess/flow/runApplication'
import {
  getFlowDetailInfoAPI,
  getFlowFormDataAPI,
  getFlowFormOptionsAPI,
} from '@/api/ess/flow/todo'
import { paramsFilter, validateNull } from '@/utils/common'
// import FlowFormRendering from '@/pages-flow/components/form-create/flow-form-rendering/flow-form-rendering.vue'
import FlowFormRendering from '../../components/form-create/flow-form-rendering/flow-form-rendering.vue'
import AuditPopup from './components/AuditPopup.vue'
import FileBatchPublish from './components/FileBatchPublish.vue'
import FlowSteps from './components/FlowSteps.vue'
import FlowSubmitFooter from './components/FlowSubmitFooter.vue'
import SelfFileListPublish from './components/SelfFileListPublish.vue'
import TemplatePublishFile from './components/TemplatePublishFile.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
const message = useMessage()

const loading = ref(true)
const isHiJob = ref('1') // 0是处理 1是查看
const form = ref({})
const curForm = ref({})
const formData = ref({}) // 表单数据
const formSettingData = ref({})
const nodeSettings = ref({}) // 节点设置
async function getData() {
  try {
    const { id, flowInstId } = form.value
    const res = await getFlowDetailInfoAPI({ id, isHiJob: 0, flowInstId })
    const formDataRes = await getFlowFormDataAPI(res.object.orderId)
    const formSettingRes = await getFlowFormOptionsAPI({
      type: 1,
      formType: 0,
      formId: formDataRes.object.formId,
      flowInstId: formDataRes.object.flowInstId,
      defFlowId: formDataRes.object.defFlowId,
      flowNodeId: res.object.flowNodeId,
      isOnlyCurr: 1,
    })
    curForm.value = res.object
    form.value = formDataRes.object
    nodeSettings.value = formSettingRes.object
    formSettingData.value = res.object.elTabs.find(i => +i.type === 0) // 表单

    if (curForm.value.status === '1' && +isHiJob.value === 0) {
      message.alert('当前任务已办理！')
    }
  }
  finally {
    loading.value = false
  }
}

const auditPopupRef = ref()
const formRef = ref()

// 判断页面是否可编辑
const isAutoSave = computed(() => {
  return +formSettingData.value?.isFormEdit === 1
})
function handleShowAudit(type, isStart) {
  const jobBtnMaps = {
    agree: 0, // 同意
    reject: 18, // 拒绝
    backPre: 10, // 退回上一步
  }
  curForm.value.jobBtn = jobBtnMaps[type]
  if (isStart) { // 被退回开始节点
    showConfirmSubmit()
  }
  else {
    if (isAutoSave.value && type === 'agree') {
      formRef.value.handleSubmit().then((res) => {
        auditPopupRef.value.open(type)
        formData.value = res
      })
    }
    else {
      auditPopupRef.value.open(type)
    }
  }
}

function saveInitData() {
  const submitForm = formRef.value.getFormData()
  const e = paramsFilter(submitForm)
  const formJson = JSON.stringify(e)
  const result = { ...form.value, formData: formJson }
  return result
}

async function submitComment(comment) {
  if (!isAutoSave.value)
    return

  try {
    uni.showLoading()
    const saveData = saveInitData()
    await saveFlowRunFormAPI(saveData)

    const { jobBtn } = curForm.value
    const completeParams = { ...curForm.value, comment, jobBtn }

    const actionMap = {
      10: () => backPreAPI(completeParams),
      18: () => rejectJobAPI({ ...completeParams, flowStatus: '0' }),
      default: () => completeJobAPI(completeParams),
    }

    const action = actionMap[jobBtn] || actionMap.default
    const res = await action()

    if (+res.code === 200) {
      const messageMap = {
        10: '已成功退回上一步！',
        18: '已拒绝',
        default: '操作成功',
      }

      const title = messageMap[jobBtn] || messageMap.default
      message.alert({ title, msg: '即将返回待办列表' }).then(() => {
        uni.navigateBack()
      })
    }
  }
  catch (error) {
    console.error('提交审批意见失败:', error)
  }
  finally {
    uni.hideLoading()
  }
}

function showConfirmSubmit() {
  message.confirm({
    title: '确认提交吗？',
    msg: '确认提交后，将无法再修改内容',
  }).then(() => {
    submitComment()
  })
}

// 是否属于使用模版
const isUseTemplate = computed(() => {
  return form.value.path === '/template/useTemplate/components/Steps/FillApplicationInfo'
})
// 是否属于文件发起
const isFileLunch = computed(() => {
  return form.value.path === '/fileManagement/fileLunch/components/Steps/FillApplicationInfo'
})
// 是否属于文件批量发起
const isFileBatchLunch = computed(() => {
  return form.value.path === '/fileManagement/fileBatchLunch/components/Steps/FillApplicationInfo'
})

onLoad((option) => {
  console.log(option)
  form.value = option
  isHiJob.value = option.isHiJob
  getData()
})
</script>

<template>
  <view class="app-container">
    <fg-navbar>详情</fg-navbar>
    <fg-skeleton v-if="loading" col-height="50rpx" :row-count="10" />
    <view v-if="curForm.order" class="page-main">
      <view class="apply-user-info">
        <view class="apply-user-info-title" :class="{ 'p-r-140rpx': isHiJob === '0' }">
          {{ curForm.order.createUserName }}({{
            curForm.order.createUserWorkNumber
          }})提交的{{ curForm.order.formName }}
        </view>
        <view v-if="isHiJob === '0'" class="details-tag">
          待我处理
        </view>
        <view class="user-dept">
          {{ curForm.order.createUserDeptName }}
        </view>
        <view class="apply-form">
          <view class="form-item">
            <span class="form-item-label">审批工号：</span>
            <span class="form-item-content">{{ curForm.order.code }}</span>
          </view>
          <view class="form-item">
            <span class="form-item-label">申请时间：</span>
            <span class="form-item-content">{{ curForm.receiveTime }}</span>
          </view>
        </view>
      </view>
      <view v-if="form.formInfo" class="apply-form-base">
        <view class="box-title">
          申请基本信息
        </view>
        <flow-form-rendering
          ref="formRef"
          v-model="formData"
          :rules="form.formInfo"
          :form-data="form.formData"
          :options="form.formInfo"
          :form-base-info="curForm"
          :node-settings="nodeSettings"
          :disabled="!isAutoSave || +isHiJob === 1"
        />
      </view>
      <!-- 模版使用 -->
      <TemplatePublishFile v-else-if="isUseTemplate" :form="curForm" />
      <!-- 文件发起 -->
      <SelfFileListPublish v-else-if="isFileLunch" :form="curForm" />
      <!-- 文件批量发起 -->
      <FileBatchPublish v-else-if="isFileBatchLunch" :form="curForm" />

      <view class="flow-step">
        <view class="box-title">
          流程
        </view>
        <FlowSteps :flow-inst-id="form.flowInstId" />
      </view>

      <FlowSubmitFooter v-if="+isHiJob === 0" class="handle-box" :form-base-info="curForm" @change="handleShowAudit" />
    </view>

    <AuditPopup ref="auditPopupRef" @submit="submitComment" />
  </view>
</template>

<style lang="scss" scoped>
.page-main {
  padding: 10rpx 30rpx 150rpx;
}

.white-round-box {
  background: #fff;
  border-radius: 18rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  position: relative;
}

.apply-user-info {
  @extend .white-round-box;
  .details-tag {
    position: absolute;
    right: 0;
    top: 25rpx;
    font-size: 24rpx;
    padding: 7rpx 18rpx 7rpx 20rpx;
    border-radius: 100rpx 0 0 100rpx;
    background: #ffefe3;
    color: #ff7912;
  }
  .apply-user-info-title {
    @apply title-line;
  }
  .user-dept {
    background: #f0f1f3;
    padding: 7rpx 20rpx;
    @apply inline-block text-center min-w-90rpx text-24rpx color-#6B6B6B rounded-50rpx m-y-20rpx;
  }
  .apply-form {
    font-size: 26rpx;
    .form-item {
      margin-bottom: 10rpx;
      .form-item-label {
        color: #6b6b6b;
        margin-right: 50rpx;
      }
    }
  }
}

.apply-form-base {
  @extend .white-round-box;
  padding: 20rpx 10rpx;
  .box-title {
    padding-left: 20rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}

.flow-step {
  @extend .white-round-box;
  .box-title {
    font-weight: bold;
    margin-bottom: 20rpx;
  }
}
</style>
