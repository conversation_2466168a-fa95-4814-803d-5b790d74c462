<script lang="ts" setup>
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps<{
  modelValue: [Array<string> | string, string]
  labelWidth: string
  title: string
  field: any
}>()

const users = computed(() => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue
  }
  return [props.modelValue]
})
</script>

<template>
  <wd-cell :title-width="labelWidth" :title="title">
    <view v-for="item in users" :key="item.id" class="">
      {{ item.nickName || item.name }}
    </view>
  </wd-cell>
</template>

<style lang="scss" scoped>
//
</style>
