<script lang="ts" setup name="TemplatePublishFile">
import { queryFormDataByOrderId } from '@/api/ess/flow/runApplication'
import { exportOriginDataAPI } from '@/api/ess/flow/todo'
import { toast } from '@/utils/toast'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
})
const formData = ref({} as any)
const loading = ref(true)

async function getData() {
  try {
    const res = await queryFormDataByOrderId(props.form.orderId)
    formData.value = res.object
  }
  finally {
    loading.value = false
  }
}

// H5 环境下载文件函数
function downloadFileInH5(arrayBuffer: ArrayBuffer, fileName: string) {
  // 创建 Blob 对象
  const blob = new Blob([arrayBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  })

  // 创建下载链接
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName

  // 触发下载
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  // 显示成功提示
  toast.success('文件下载成功')
}

// 统一的 Excel 文件打开函数
function openExcelFile(filePath: string) {
  uni.openDocument({
    filePath,
    fileType: 'xlsx',
    fail() {
      toast.error('文件打开失败')
    },
  })
}

// 下载签署方
async function downloadSigner() {
  uni.showLoading({
    title: '下载中...',
  })

  try {
    const res = await exportOriginDataAPI(formData.value.flowBatchPublishId)
    uni.hideLoading()

    if (res) {
      try {
        // 获取二进制数据
        const binaryData = res instanceof ArrayBuffer ? res : res.data as ArrayBuffer

        // 检查是否支持文件系统管理器
        if (typeof uni.getFileSystemManager === 'function') {
          // 小程序环境：使用文件系统管理器写入文件
          const fs = uni.getFileSystemManager()
          const tempFilePath = `${wx.env.USER_DATA_PATH}/签署方信息.xlsx`

          fs.writeFileSync(tempFilePath, binaryData)
          openExcelFile(tempFilePath)
        }
        else {
          // H5 环境：使用浏览器下载
          // #ifdef H5
          downloadFileInH5(binaryData, '签署方信息.xlsx')
          // #endif
        }
      }
      catch (error) {
        toast.error('文件处理失败')
      }
    }
  }
  catch (err) {
    uni.hideLoading()
    toast.error('下载失败')
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <view class="flow-custom">
    <fg-skeleton v-if="loading" col-height="50rpx" :row-count="10" />
    <wd-cell-group v-else>
      <view class="base-info overflow-hidden rounded-18rpx">
        <wd-cell vertical title="标题" :value="formData.flowTitle" />
        <wd-cell vertical title="申请原由" :value="formData.applyReason" />
        <wd-cell vertical title="模版名称" :value="formData.templateName" />
        <view v-if="formData.extParam" class="inline-form-item">
          <text class="color-#6B6B6B">
            批量发起{{ formData.extParam.signerCount }}份：
          </text>
          <wd-text text="下载签署方" type="primary" decoration="underline" line-height="32rpx" @click="downloadSigner" />
        </view>
        <view v-if="formData.deadline" class="inline-form-item">
          <text class="color-#6B6B6B">
            签署截止时间：
          </text>
          <text>
            审批通过后 {{ formData.deadline }} 天
          </text>
        </view>
      </view>
    </wd-cell-group>
  </view>
</template>

<style lang="scss" scoped>
.flow-custom {
  margin-bottom: 20rpx;
  :deep(.wd-cell-group) {
    background: transparent;
    .wd-cell-group__body {
      background: transparent;
    }
  }
  .base-info {
    background: #fff;
    :deep() {
      .wd-cell__wrapper.is-vertical {
        padding-bottom: 0;
        .wd-cell__right {
          background: #f9f9f9;
          border-radius: 10rpx;
          margin-top: 10rpx;
          padding: 16rpx 30rpx;
        }
      }
    }
  }
  .inline-form-item {
    font-size: 26rpx;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
  }
}
</style>
