<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '电子签章平台',
  },
}
</route>

<script lang="ts" setup>
const webviewUrl = ref('')

onLoad((options) => {
  webviewUrl.value = decodeURIComponent(options.url)
})
</script>

<template>
  <view class="">
    <web-view v-if="webviewUrl" :src="webviewUrl" />
  </view>
</template>

<style lang="scss" scoped>
//
</style>
