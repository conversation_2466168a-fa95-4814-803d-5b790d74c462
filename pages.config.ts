import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'
import { tabBar } from './src/layouts/fg-tabbar/tabbarList'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: '电子签章平台',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#F9F9F9',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^fg-(.*)': '@/components/fg-$1/fg-$1.vue',
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  usingComponents: {
    'confirm-modal': '@/components/confirm-modal/index.vue',
  },
  // tabbar 的配置统一在 “./src/layouts/fg-tabbar/tabbarList.ts” 文件中
  tabBar: tabBar as any,
  // 分包预加载配置
  preloadRule: {
    // 流程相关分包 - 用户登录后很可能会使用，优先级高
    'pages-flow': {
      network: 'all', // 在所有网络环境下预加载（包括2G/3G/4G/5G/wifi）
      packages: ['pages-flow'], // 预加载的分包名称
    },
    // 演示/其他功能分包 - 使用频率较低，仅在wifi环境下预加载
    'pages-sub': {
      network: 'wifi', // 仅在 wifi 环境下预加载，节省用户流量
      packages: ['pages-sub'],
    },
  },
})
