<script lang="ts" setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  signOrder: {
    type: Number,
    default: 0, //  0-有序 1-无序
  },
})

// 签名属性格式化
function formatterSignAttr(attrs, item) {
  if (attrs) {
    return attrs.join('/')
  }
  else {
    return item.organizationName ? '盖章' : '签字'
  }
}

// 获取签字类型
function getApproveType(item: any) {
  if (item.approveType === 0 && item.workNumber) {
    return 'approve-item-green'
  }
  else if (item.approveType === 0 && item.organizationName) {
    return 'approve-item-blue'
  }
  else {
    return 'approve-item-orange'
  }
}
</script>

<template>
  <view class="approve-list">
    <view
      v-for="(item, index) in list"
      :key="index"
      class="approve-item"
      :class="[getApproveType(item), { 'is-order': signOrder === 0 }]"
    >
      <view v-if="signOrder === 0" class="approve-item-order">
        {{ index + 1 }}
      </view>
      <view>
        <view class="font-600">
          {{ item.approveName }} {{ item.workNumber }}
        </view>
        <view class="mt-5rpx text-24rpx color-#6B6B6B">
          {{ item.organizationName || "个人签名" }}
        </view>
      </view>
      <view class="flex-shrink-0">
        {{ formatterSignAttr(item.signAttributes, item) }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
$green: #24a87e;
$blue: #2e89ff;
$orange: #fb9547;

.approve-list {
  padding: 0 var(--wot-cell-padding) 20rpx var(--wot-cell-padding);
}

.approve-item {
  @apply flex-x-between text-26rpx p-y-18rpx p-x-30rpx rounded-13rpx mb-10rpx relative;

  &-green {
    background-color: #f2f9f6;
    border-left: 8rpx solid $green;
  }

  &-blue {
    background-color: #f1f7fd;
    border-left: 8rpx solid $blue;
  }

  &-orange {
    background-color: #fef8f4;
    border-left: 8rpx solid $orange;
  }

  // 序号模式样式
  &.is-order {
    padding-left: 46rpx;
    border-left: 0;
    overflow: hidden;

    .approve-item-order {
      padding: 1rpx 6rpx;
      border-radius: 13rpx 0 13rpx 0;
      @apply absolute left-0 top-0 min-w-35rpx box-border text-center text-white text-24rpx font-500;
    }

    &.approve-item-green .approve-item-order {
      background: $green;
    }

    &.approve-item-blue .approve-item-order {
      background: $blue;
    }

    &.approve-item-orange .approve-item-order {
      background: $orange;
    }
  }
}
</style>
