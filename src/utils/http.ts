import type { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store'

export function http<T>(options: CustomRequestOptions) {
  // 判断是否为二进制流请求
  const isBinaryRequest = options.responseType === 'arraybuffer'

  // 1. 返回 Promise 对象
  return new Promise<IResData<T> | any>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: isBinaryRequest ? undefined : 'json',
      // #ifndef MP-WEIXIN
      responseType: options.responseType || 'json',
      // #endif
      // 响应成功
      success(res) {
        // 二进制流请求直接返回原始响应
        if (isBinaryRequest) {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          }
          else {
            reject(new Error(`系统错误，请重试`))
          }
          return
        }

        // 普通 JSON 请求的处理逻辑
        const responseData = res.data as any
        const statusCode = +responseData.code
        // 状态码 2xx，参考 axios 的设计
        if (statusCode >= 200 && statusCode < 300) {
          resolve(responseData as IResData<T>)
        }
        else if (statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          useUserStore().removeUserInfo()
          uni.navigateTo({ url: '/pages/login/index' })
          reject(new Error('Unauthorized'))
        }
        else {
          // 其他错误 -> 根据后端错误信息轻提示
          const msg = responseData.msg || responseData.message
          !options.hideErrorToast
          && uni.showToast({
            icon: 'none',
            title: msg || '系统未知错误，请反馈给管理员',
          })
          reject(new Error(msg || '请求失败'))
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpGet<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
    ...options,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export function httpPost<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
    ...options,
  })
}
/**
 * PUT 请求
 */
export function httpPut<T>(url: string, data?: Record<string, any>, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    data,
    query,
    method: 'PUT',
    header,
    ...options,
  })
}

/**
 * DELETE 请求（无请求体，仅 query）
 */
export function httpDelete<T>(url: string, query?: Record<string, any>, header?: Record<string, any>, options?: Partial<CustomRequestOptions>) {
  return http<T>({
    url,
    query,
    method: 'DELETE',
    header,
    ...options,
  })
}

http.get = httpGet
http.post = httpPost
http.put = httpPut
http.delete = httpDelete
