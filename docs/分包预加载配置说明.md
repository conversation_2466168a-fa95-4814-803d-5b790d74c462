# 分包预加载配置说明

## 概述

分包预加载是微信小程序提供的一种优化技术，可以在用户进入小程序时提前下载指定的分包，从而提升用户体验。

## 配置位置

分包预加载配置在 `pages.config.ts` 文件中的 `preloadRule` 字段：

```typescript
preloadRule: {
  // 流程相关分包 - 用户登录后很可能会使用，优先级高
  'pages-flow': {
    network: 'all', // 在所有网络环境下预加载
    packages: ['pages-flow'], // 预加载的分包名称
  },
  // 演示/其他功能分包 - 使用频率较低，仅在wifi环境下预加载
  'pages-sub': {
    network: 'wifi', // 仅在 wifi 环境下预加载
    packages: ['pages-sub'],
  },
}
```

## 配置参数说明

### network 参数
- `all`: 在所有网络环境下预加载（2G/3G/4G/5G/wifi）
- `wifi`: 仅在 wifi 环境下预加载，节省用户流量

### packages 参数
- 数组格式，指定要预加载的分包名称
- 分包名称对应 `subPackages` 中的 `root` 字段

## 分包结构

当前项目的分包结构：

```
src/
├── pages/              # 主包页面
├── pages-flow/         # 流程相关分包（待办、审批等）
└── pages-sub/          # 其他功能分包（演示页面等）
```

## 预加载策略

### pages-flow 分包
- **预加载条件**: 所有网络环境
- **原因**: 用户登录后很可能会使用待办、审批等功能
- **优先级**: 高

### pages-sub 分包
- **预加载条件**: 仅 wifi 环境
- **原因**: 使用频率较低，避免消耗用户流量
- **优先级**: 低

## 注意事项

1. **分包大小限制**: 每个分包不能超过 2MB
2. **预加载时机**: 在小程序启动时或进入主包页面时触发
3. **网络环境检测**: 小程序会自动检测当前网络环境
4. **用户体验**: 预加载可以减少用户点击分包页面时的等待时间

## 监控和调试

### 开发者工具
1. 打开微信开发者工具
2. 在 Network 面板中可以看到分包的下载情况
3. 在 Console 中可以看到预加载的日志

### 真机调试
1. 在真机上打开小程序
2. 观察分包页面的加载速度
3. 可以通过 `wx.getNetworkType()` 检测当前网络环境

## 性能优化建议

1. **合理设置预加载条件**: 根据分包的使用频率和大小来设置
2. **监控分包大小**: 定期检查分包大小，避免超过限制
3. **用户行为分析**: 根据用户行为数据调整预加载策略
4. **A/B 测试**: 可以通过 A/B 测试来验证预加载效果

## 相关文件

- `pages.config.ts`: 分包预加载配置
- `vite.config.ts`: 分包构建配置
- `src/pages.json`: 生成的页面配置文件（自动生成，不要手动修改）
