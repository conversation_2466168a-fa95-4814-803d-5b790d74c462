import { essPrefix, json<PERSON>lowOrder, jsonFlowPrefix } from '@/config/constant'
import { http } from '@/utils/http'

// 任务完成审批---保存表单
export function saveFlowRunFormAPI(data?: object) {
  return http.put(`${jsonFlowOrder}/run-application`, data)
}
// 任务审批-同意
export function completeJobAPI(data?: object) {
  return http.post(`${jsonFlowPrefix}/run-job/complete`, data)
}
// 任务审批-退回上一步
export function backPreAPI(data?: object) {
  return http.post(`${jsonFlowPrefix}/run-job/back-pre`, data)
}
// 任务审批-拒绝
export function rejectJobAPI(data?: object) {
  return http.put(`${jsonFlowPrefix}/run-flow/terminate`, data)
}
// 自定义表单-根据orderId获取表单详情
export function queryFormDataByOrderId(orderId?: string) {
  return http.get(`${jsonFlowOrder}/run-application/queryFormDataByOrderId?orderId=${orderId}`)
}
